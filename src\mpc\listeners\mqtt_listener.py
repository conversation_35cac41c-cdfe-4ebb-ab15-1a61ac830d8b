import logging

from paho.mqtt import client as mqtt_client

from src.config.configs import MQTTConfig


class MQTTListener:
    def __init__(self, config: MQTTConfig, on_message_callback=None):
        self.config = config
        self.on_message_callback = on_message_callback
        self.logger = logging.getLogger(__name__)

        self.client = mqtt_client.Client()

        # 设置认证信息
        if self.config.user_name and self.config.password:
            self.client.username_pw_set(self.config.user_name, self.config.password)
        self.client.on_connect = self._on_connect
        self.client.on_message = self._on_message
        self.client.on_disconnect = self._on_disconnect

    def _subscribe(self, topic):
        if isinstance(self.config.topic, str):
            self.client.subscribe(self.config.topic)
            self.logger.info(f"已订阅主题: '{self.config.topic}'")
        elif isinstance(self.config.topic, list):
            for t in self.config.topic:  # type: ignore
                self.client.subscribe(t)
                self.logger.info(f"已订阅主题: '{t}'")

    def _on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            self.logger.info("成功连接到MQTT代理")
            self._subscribe(self.config.topic)
        else:
            self.logger.error(f"连接失败，返回码: {rc}")

    def _on_message(self, client, userdata, message):
        topic = message.topic
        payload = message.payload.decode("utf-8")
        self.logger.debug(f"收到消息，主题: '{topic}', 内容: '{payload}'")

        if self.on_message_callback is not None:
            try:
                self.on_message_callback(topic, payload)
            except Exception as e:
                self.logger.error(f"处理消息时发生错误: {e}")

    def _on_disconnect(self, client, userdata, rc):
        """断开连接时的回调函数"""
        if rc != 0:
            self.logger.warning(f"意外断开连接 (rc={rc})。paho-mqtt将自动尝试重连...")
        else:
            self.logger.info("已主动断开连接")

    def start(self):
        try:
            self.client.connect(self.config.broker, self.config.port, self.config.keep_alive)
            self.client.loop_forever()
        except ConnectionRefusedError:
            self.logger.error("连接被拒绝。请检查代理地址、端口和网络设置。")
        except OSError as e:
            self.logger.error(f"连接时发生操作系统错误: {e}。请检查代理地址是否正确。")
        except KeyboardInterrupt:
            self.logger.warning("收到中断信号，正在停止服务...")
        except Exception as ex:
            self.logger.error(f"连接时发生未知错误: {ex}")
        finally:
            self.stop()

    def stop(self):
        self.client.disconnect()
        self.logger.info("MQTT监听器已停止")
