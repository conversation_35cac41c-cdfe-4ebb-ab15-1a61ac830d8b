import glob
import json
import logging
import os
from pathlib import Path

import lightning as L
import numpy as np
import pandas as pd
import torch
from torch.utils.data import DataLoader

from src.config import Seq2SeqConfig, TrainerConfig
from src.consts.file_name import FileNameEnum as fne
from src.trainer.dataset import RandomSubsetSampler, Seq2SeqDataset
from src.trainer.model import LitSeq2Seq, LitSeq2SeqWithAttention
from src.utils.data_utils import denormalize, normalize_by_params
from src.utils.metrics_utils import calculate_metrics
from src.utils.sns_plot import SeabornVisualizer


class Seq2SeqPredictor:
    def __init__(self, model_dir: str):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.model_dir = Path(model_dir)
        self.meta_dir = self.model_dir / "meta"

        self.normalize_meta: dict = self._load_normalize_meta()
        self.train_config: TrainerConfig = self._load_train_config()
        self.model_config: Seq2SeqConfig = self._load_model_config()
        self.model = self._load_model()
        self.model.eval()

        self.input_size = self.train_config.input_len
        self.output_size = self.train_config.output_len
        self.x_past_feature_size = len(self.train_config.x_past_columns)
        self.x_future_feature_size = len(self.train_config.x_future_columns)
        self.y_future_feature_size = len(self.train_config.y_future_columns)

    def _load_model(self) -> L.LightningModule:
        try:
            model_classes: list[type[L.LightningModule]] = [
                LitSeq2Seq,
                LitSeq2SeqWithAttention,
            ]

            if self.train_config is None or self.model_config is None:
                raise ValueError("训练配置和模型配置未加载")

            model_file = self.model_dir / f"{fne.SEQ2SEQ_NAME.value}.ckpt"
            if not model_file.exists():
                raise FileNotFoundError(f"模型文件不存在: {model_file}")

            model = None
            for model_class in model_classes:
                try:
                    model = model_class.load_from_checkpoint(
                        str(model_file),
                        num_past_features=len(self.train_config.x_past_columns),
                        num_future_features=len(self.train_config.x_future_columns),
                        num_target_features=len(self.train_config.y_future_columns),
                        hidden_dim=self.model_config.hidden_size,
                        num_layers=self.model_config.num_layers,
                        dropout_rate=self.model_config.dropout,
                        learning_rate=self.model_config.learning_rate,
                        weight_decay=self.model_config.weight_decay,
                        loss_type=self.model_config.loss_type,
                        scheduler_type=self.model_config.scheduler_type,
                    )

                    self.logger.info(f"成功加载模型: {model_class.__name__}")
                    break
                except Exception as e:
                    self.logger.debug(f"尝试加载 {model_class.__name__} 失败: {e}")
                    continue

            if model is None:
                raise RuntimeError("无法加载模型，请检查模型文件格式")
            else:
                return model

        except Exception as e:
            self.logger.error(f"加载模型失败: {e}")
            raise e

    def _load_normalize_meta(self) -> dict:
        try:
            # 加载归一化数据
            meta_file = self.meta_dir / f"{fne.NORMALIZE_META.value}.json"
            if not meta_file.exists():
                raise FileNotFoundError(f"元数据文件不存在: {meta_file}")
            with open(meta_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"加载归一化元数据失败: {e}")
            raise e

    def _load_train_config(self) -> TrainerConfig:
        try:
            train_config_file = self.meta_dir / f"{fne.TRAIN_CONFIG_META.value}.json"
            if not train_config_file.exists():
                raise FileNotFoundError(f"训练配置文件不存在: {train_config_file}")
            with open(train_config_file, "r", encoding="utf-8") as f:
                train_config_json = json.load(f)
                return TrainerConfig(**train_config_json)
        except Exception as e:
            self.logger.error(f"加载训练配置失败: {e}")
            raise e

    def _load_model_config(self) -> Seq2SeqConfig:
        try:
            model_config_file = self.meta_dir / f"{fne.MODEL_CONFIG_META.value}.json"
            if not model_config_file.exists():
                raise FileNotFoundError(f"模型配置文件不存在: {model_config_file}")
            with open(model_config_file, "r", encoding="utf-8") as f:
                model_config_json = json.load(f)
                return Seq2SeqConfig(**model_config_json)
        except Exception as e:
            self.logger.error(f"加载模型配置失败: {e}")
            raise e

    def reload_metadata(self) -> None:
        try:
            self.normalize_meta = self._load_normalize_meta()
            self.train_config = self._load_train_config()
            self.model_config = self._load_model_config()

            self.logger.info("成功加载元数据")

        except Exception as e:
            self.logger.error(f"加载元数据失败: {e}")
            raise e

    def predict_df_norm(self, x_past_df: pd.DataFrame, x_future_df: pd.DataFrame) -> pd.DataFrame:
        """
        单步预测, 输入和输出均为原始数据
        """
        try:
            x_past_df_normed = self.normalize_data(x_past_df)
            x_future_df_normed = self.normalize_data(x_future_df)
            y_futrue = self.predict_df(x_past_df_normed, x_future_df_normed)
            return self.denormalize_data(y_futrue)
        except Exception as e:
            self.logger.error(f"模型预测错误：{e}")
            raise e

    def predict_df(self, x_past_df: pd.DataFrame, x_future_df: pd.DataFrame) -> pd.DataFrame:
        """
        预测-基于Pandas的DataFrame类型, 输入和输出均为归一化结果
        """
        try:
            if len(x_past_df) < self.input_size:
                raise ValueError("输入数据长度不足")

            if len(x_future_df) < self.output_size:
                raise ValueError("输入数据长度不足")

            x_past = (
                x_past_df.tail(self.input_size)[self.train_config.x_past_columns].to_numpy().reshape(1, self.input_size, self.x_future_feature_size)
            )
            x_future = (
                x_future_df.head(self.output_size)[self.train_config.x_future_columns]
                .to_numpy()
                .reshape(1, self.output_size, self.x_future_feature_size)
            )
            predictions = self.predict_ndarray(x_past, x_future)
            return pd.DataFrame(predictions, columns=self.train_config.y_future_columns)

        except Exception as e:
            self.logger.error(f"模型预测错误：{e}")
            raise e

    def predict_ndarray(self, x_past: np.ndarray, x_future: np.ndarray) -> np.ndarray:
        """
        预测-基于Numpy的ndarray类型, 输入和输出均为归一化结果

        params:
            x_past: np.ndarray, 形状为 [batch_size, PAST_SEQ_LEN, num_past_features]
            x_future: np.ndarray, 形状为 [batch_size, FUTURE_SEQ_LEN, num_future_features]
        """
        try:
            X_past_tensor = torch.FloatTensor(x_past)
            X_future_tensor = torch.FloatTensor(x_future)

            with torch.no_grad():
                predictions = self.model(X_past_tensor, X_future_tensor).numpy()

            return predictions
        except Exception as e:
            self.logger.error(f"模型预测错误：{e}")
            raise e

    def normalize_data(self, data: pd.DataFrame) -> pd.DataFrame:
        try:
            if self.normalize_meta is None:
                raise ValueError("归一化元数据未加载")
            return normalize_by_params(data, self.normalize_meta)
        except Exception as e:
            self.logger.error(f"数据归一化失败: {e}")
            raise e

    def denormalize_data(self, data: pd.DataFrame) -> pd.DataFrame:
        try:
            if self.normalize_meta is None:
                raise ValueError("归一化元数据未加载")
            return denormalize(data, self.normalize_meta)
        except Exception as e:
            self.logger.error(f"数据反归一化失败: {e}")
            raise e

    def get_train_config(self) -> TrainerConfig:
        return self.train_config

    def get_model_config(self) -> Seq2SeqConfig:
        return self.model_config


class Seq2SeqEvaluator:
    def __init__(self, model_dir: str, data_file_name: str, samples_num: int = 0):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.model_dir = Path(model_dir)
        self.data_dir = self.model_dir / "data"
        self.visual_dir = self.model_dir / "visualizations"
        self.data_file_name = data_file_name
        self.samples_num = samples_num

        # 加载模型
        self.predictor = Seq2SeqPredictor(model_dir)
        self.train_config = self.predictor.get_train_config()

        # 用于评估的数据
        self.datas: list[pd.DataFrame] = self.load_data()

        # 用于绘制的可视化工具
        self.visualizer = SeabornVisualizer()

        os.makedirs(self.visual_dir, exist_ok=True)

    def load_data(self) -> list[pd.DataFrame]:
        try:
            files = sorted(glob.glob(str(self.data_dir / f"{self.data_file_name}.csv")))
            if not files:
                raise FileNotFoundError(f"未找到数据文件: {self.data_file_name}")

            dfs = []
            for file_path in files:
                df = pd.read_csv(file_path)
                dfs.append(df)

            return dfs
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
            raise e

    def evaluate(self):
        try:
            self.logger.info("开始评估模型...")

            if len(self.datas) == 0:
                raise ValueError("没有可用的数据进行评估")

            normalized_dfs = []
            for df in self.datas:
                df_normalized = self.predictor.normalize_data(df)
                normalized_dfs.append(df_normalized)

            dataset = Seq2SeqDataset(
                normalized_dfs,
                self.train_config.x_past_columns,
                self.train_config.x_future_columns,
                self.train_config.y_future_columns,
                input_len=self.train_config.input_len,
                output_len=self.train_config.output_len,
            )

            sampler = RandomSubsetSampler(dataset.length, self.samples_num)
            dataloader = DataLoader(dataset, batch_size=64, sampler=sampler, num_workers=4)

            y_trues = []
            y_preds = []

            for batch_idx, batch in enumerate(dataloader):
                x_past, x_future, y_future = batch
                y_true_batch = y_future.numpy()
                y_pred_batch = self.predictor.predict_ndarray(x_past, x_future)

                for y_true, y_pred in zip(y_true_batch, y_pred_batch):
                    y_trues.append(y_true)
                    y_preds.append(y_pred)

            total_number = len(y_trues)
            for i, (y_true, y_pred) in enumerate(zip(y_trues, y_preds)):
                self.logger.info(f"正在处理样本 {i + 1}/{total_number}")

                y_true_df = pd.DataFrame(y_true, columns=self.train_config.y_future_columns)
                y_pred_df = pd.DataFrame(y_pred, columns=self.train_config.y_future_columns)

                # 反归一化
                y_true_df = self.predictor.denormalize_data(y_true_df)
                y_pred_df = self.predictor.denormalize_data(y_pred_df)

                self._log_metrics(y_true_df, y_pred_df, self.train_config.y_future_columns)
                self._plot_prediction(i, y_true_df, y_pred_df, self.train_config.y_future_columns)

            self.logger.info("评估完成...")
        except Exception as e:
            self.logger.error(f"评估失败: {e}")

    def _log_metrics(self, y_true: pd.DataFrame, y_pred: pd.DataFrame, columns: list[str]):
        try:
            for column in columns:
                y_t = y_true[column].to_numpy()
                y_p = y_pred[column].to_numpy()

                metrics = calculate_metrics(y_t, y_p)
                self.logger.info(f"预测目标: {column}")
                self.logger.info(f"    整体指标: {metrics.to_str()}")

                for i, (y_t_step, y_p_step) in enumerate(zip(y_t, y_p)):
                    metrics_step = calculate_metrics(y_t_step, y_p_step)
                    self.logger.info(f"    第{i}步指标: {metrics_step.to_str()}")

        except Exception as e:
            self.logger.error(f"计算指标失败: {e}")

    def _plot_prediction(self, index: int, y_true: pd.DataFrame, y_pred: pd.DataFrame, columns: list[str]):
        try:
            self.visualizer.plot_line_chart(y_true, y_pred, columns, ci=5, suptitle="预测结果对比图")
            self.visualizer.save(self.visual_dir / f"evaluation-{index}.png")
            self.visualizer.close()
        except Exception as e:
            self.logger.error(f"绘制预测图失败: {e}")
