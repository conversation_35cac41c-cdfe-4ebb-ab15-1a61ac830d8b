import logging
import os
from datetime import datetime
from typing import Optional

from src.config import Configs
from src.consts import FileNameEnum as fne
from src.trainer.data_loader import Seq2SeqLoader
from src.trainer.model import LitSeq2SeqWithAttention

from .trainer import train_model

MODEL_NAME = fne.SEQ2SEQ_NAME.value
Logger = logging.getLogger(__name__)


def train_seq2seq_model(start_time: datetime, end_time: datetime, work_dir: Optional[str] = None):
    train_config = Configs.get_train_config()
    seq2seq_config = Configs.get_seq2seq_config()

    if work_dir is None:
        work_dir = f"data/test-{MODEL_NAME.lower()}/{datetime.now().strftime('%Y%m%d')}"
    os.makedirs(work_dir, exist_ok=True)

    # 创建数据加载器
    data_loader = Seq2SeqLoader(start_time=start_time, end_time=end_time, work_dir=work_dir)
    # data_loader.prepare_data()
    data_loader.setup()

    # 创建模型
    # model = LitSeq2Seq(
    #     num_past_features=len(data_loader.config.x_past_columns),
    #     num_future_features=len(data_loader.config.x_future_columns),
    #     num_target_features=len(data_loader.config.y_future_columns),
    #     hidden_dim=seq2seq_config.hidden_size,
    #     num_layers=seq2seq_config.num_layers,
    #     dropout_rate=seq2seq_config.dropout,
    #     learning_rate=seq2seq_config.learning_rate,
    #     weight_decay=seq2seq_config.weight_decay,
    #     loss_type=seq2seq_config.loss_type,
    #     scheduler_type=seq2seq_config.scheduler_type,
    # )

    model = LitSeq2SeqWithAttention(
        num_past_features=len(data_loader.config.x_past_columns),
        num_future_features=len(data_loader.config.x_future_columns),
        num_target_features=len(data_loader.config.y_future_columns),
        hidden_dim=seq2seq_config.hidden_size,
        num_layers=seq2seq_config.num_layers,
        dropout_rate=seq2seq_config.dropout,
        learning_rate=seq2seq_config.learning_rate,
        weight_decay=seq2seq_config.weight_decay,
        loss_type=seq2seq_config.loss_type,
        scheduler_type=seq2seq_config.scheduler_type,
    )

    train_model(
        model_name=MODEL_NAME,
        work_dir=work_dir,
        model=model,
        data_loader=data_loader,
        train_config=train_config,
    )
