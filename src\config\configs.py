import tomllib
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict

_CONFIG_FILE_PATH = "config/config.toml"


@dataclass
class TrainerConfig:
    save_data: bool = True  # 是否保存训练数据数据
    freq: str = "30s"  # 数据采样频率
    min_data_length: int = 512  # 最小数据长度要求
    split_ratio: list[float] = field(default_factory=lambda: [0.8, 0.1, 0.1])  # 训练集、验证集、测试集的划分比例
    normalize_method: str = "z-score"  # 数据归一化方法
    input_len: int = 32  # 输入序列长度(回看窗口)
    output_len: int = 16  # 预测序列长度(预测步长)
    batch_size: int = 512  # 批处理大小
    epochs: int = 128  # 训练轮数
    accelerator: str = "auto"  # 自动选择加速器（CPU/GPU）
    devices: str = "auto"  # 自动选择设备数量
    gradient_clip_val: float = 1.0  # 梯度裁剪值，防止梯度爆炸
    early_stopping_patience: int = 10  # 早停patience，连续多少轮无改善后停止训练
    accumulate_grad_batches: int = 1  # 梯度累积批次，有效增大批次大小

    index_column: str = "TIME_STAMP"
    measurements: dict[str, str] = field(
        default_factory=lambda: {
            "X2_01.PLC01.DB844,DBX1056.0": "SELECTED",
            "X2_01.PLC01.DB844,STRING0": "STRIP_NAME_1",
            "X2_01.PLC01.DB844,STRING256": "STRIP_TYPE_1",
            "X2_01.PLC01.DB844,REAL512": "STRIP_LENGTH_1",
            "X2_01.PLC01.DB844,REAL516": "STRIP_WIDTH_1",
            "X2_01.PLC01.DB844,REAL520": "STRIP_THICK_1",
            "X2_01.PLC01.DB844,REAL524": "STRIP_WEIGHT_1",
            "X2_01.PLC01.DB844,STRING528": "STRIP_NAME_2",
            "X2_01.PLC01.DB844,STRING784": "STRIP_TYPE_2",
            "X2_01.PLC01.DB844,REAL1040": "STRIP_LENGTH_2",
            "X2_01.PLC01.DB844,REAL1044": "STRIP_WIDTH_2",
            "X2_01.PLC01.DB844,REAL1048": "STRIP_THICK_2",
            "X2_01.PLC01.DB844,REAL1052": "STRIP_WEIGHT_2",
            "X2_01.PLC02.DB844,REAL2": "WELD_POSITION_1",
            "X2_01.PLC02.DB844,REAL6": "WELD_POSITION_2",
            "X2_01.PLC02.DB844,INT0": "SPEED",
            "Luzi.PLC03.DB34,REAL106": "TEMP_PH",
            "Luzi.PLC03.DB34,REAL110": "TEMP_NOF1",
            "Luzi.PLC03.DB34,REAL114": "TEMP_NOF2",
            "Luzi.PLC03.DB34,REAL118": "TEMP_NOF3",
            "Luzi.PLC03.DB34,REAL122": "TEMP_NOF4",
            "Luzi.PLC03.DB34,REAL126": "TEMP_NOF5",
            "Luzi.PLC03.DB34,REAL130": "TEMP_RTF1",
            "Luzi.PLC03.DB34,REAL134": "TEMP_RTF2",
            "Luzi.PLC03.DB34,REAL138": "TEMP_SF",
            "Luzi.PLC03.DB34,REAL142": "TEMP_JCF1",
            "Luzi.PLC03.DB34,REAL146": "TEMP_JCF2",
            "Luzi.PLC03.DB34,REAL150": "TEMP_JCF3",
            "Luzi.PLC03.DB34,REAL154": "TEMP_JCF4",
            "Luzi.PLC03.DB34,REAL158": "TEMP_LTH",
            "Luzi.PLC03.DB34,REAL162": "TEMP_TDS",
            "Luzi.PLC03.DB34,REAL166": "STRIP_TEMP_NOF",
            "Luzi.PLC03.DB34,REAL170": "STRIP_TEMP_RTF",
            "Luzi.PLC03.DB34,REAL174": "STRIP_TEMP_SF",
            "Luzi.PLC03.DB34,REAL178": "STRIP_TEMP_JCF",
            "Luzi.PLC03.DB34,REAL182": "STRIP_TEMP_LTH",
            "Luzi.PLC03.DB34,REAL186": "AFR_NOF1",
            "Luzi.PLC03.DB34,REAL190": "AFR_NOF2",
            "Luzi.PLC03.DB34,REAL194": "AFR_NOF3",
            "Luzi.PLC03.DB34,REAL198": "AFR_NOF4",
            "Luzi.PLC03.DB34,REAL202": "AFR_NOF5",
            "Luzi.PLC03.DB34,REAL206": "AFR_RTF1",
            "Luzi.PLC03.DB34,REAL210": "AFR_RTF2",
            "Luzi.PLC03.DB34,REAL214": "AFR_SF",
            "Luzi.PLC03.DB34,REAL218": "HEAT_LOAD_NOF1",
            "Luzi.PLC03.DB34,REAL222": "HEAT_LOAD_NOF2",
            "Luzi.PLC03.DB34,REAL226": "HEAT_LOAD_NOF3",
            "Luzi.PLC03.DB34,REAL230": "HEAT_LOAD_NOF4",
            "Luzi.PLC03.DB34,REAL234": "HEAT_LOAD_NOF5",
            "Luzi.PLC03.DB34,REAL238": "HEAT_LOAD_RTF1",
            "Luzi.PLC03.DB34,REAL242": "HEAT_LOAD_RTF2",
            "Luzi.PLC03.DB34,REAL246": "HEAT_LOAD_SF",
            "Luzi.PLC03.DB34,REAL250": "GAS_FLOW_TOTAL",
            "Luzi.PLC03.DB34,REAL254": "GAS_PRESSURE_NOF",
            "Luzi.PLC03.DB34,REAL258": "GAS_FLOW_NOF1",
            "Luzi.PLC03.DB34,REAL262": "GAS_FLOW_NOF2",
            "Luzi.PLC03.DB34,REAL266": "GAS_FLOW_NOF3",
            "Luzi.PLC03.DB34,REAL270": "GAS_FLOW_NOF4",
            "Luzi.PLC03.DB34,REAL274": "GAS_FLOW_NOF5",
            "Luzi.PLC03.DB34,REAL278": "GAS_FLOW_RTF1",
            "Luzi.PLC03.DB34,REAL282": "GAS_FLOW_RTF2",
            "Luzi.PLC03.DB34,REAL286": "GAS_FLOW_SF",
            "Luzi.PLC03.DB34,REAL290": "AIR_FLOW_NOF1",
            "Luzi.PLC03.DB34,REAL294": "AIR_FLOW_NOF2",
            "Luzi.PLC03.DB34,REAL298": "AIR_FLOW_NOF3",
            "Luzi.PLC03.DB34,REAL302": "AIR_FLOW_NOF4",
            "Luzi.PLC03.DB34,REAL306": "AIR_FLOW_NOF5",
            "Luzi.PLC03.DB34,REAL310": "AIR_FLOW_RTF1",
            "Luzi.PLC03.DB34,REAL314": "AIR_FLOW_RTF2",
            "Luzi.PLC03.DB34,REAL318": "AIR_FLOW_SF",
            "Luzi.PLC03.DB34,REAL322": "AIR_FLOW_PH",
        }
    )
    track_columns: list[str] = field(
        default_factory=lambda: [
            "STRIP_NAME",
            "STRIP_TYPE",
            "STRIP_WIDTH",
            "STRIP_THICK",
            "STRIP_LENGTH",
            "STRIP_WEIGHT",
            "WELD",
        ]
    )
    input_columns: list[str] = field(
        default_factory=lambda: [
            # "STRIP_NAME",
            # "STRIP_TYPE",
            "STRIP_WIDTH",
            "STRIP_THICK",
            # "WELD",
            "SPEED",
            "HEAT_LOAD_NOF1",
            "HEAT_LOAD_NOF2",
            "HEAT_LOAD_NOF3",
            "HEAT_LOAD_NOF4",
            "HEAT_LOAD_NOF5",
            "HEAT_LOAD_RTF1",
            "HEAT_LOAD_RTF2",
            # "HEAT_LOAD_SF",
            "TEMP_PH",
            "TEMP_NOF1",
            "TEMP_NOF2",
            "TEMP_NOF3",
            "TEMP_NOF4",
            "TEMP_NOF5",
            "TEMP_RTF1",
            "TEMP_RTF2",
            # "TEMP_SF",
            "STRIP_TEMP_NOF",
            "STRIP_TEMP_RTF",
            # "STRIP_TEMP_SF",
        ]
    )
    x_past_columns: list[str] = field(  # seq2seq模型输入
        default_factory=lambda: [
            # "STRIP_NAME",
            # "STRIP_TYPE",
            "STRIP_WIDTH",
            "STRIP_THICK",
            # "WELD",
            "SPEED",
            "HEAT_LOAD_NOF1",
            "HEAT_LOAD_NOF2",
            "HEAT_LOAD_NOF3",
            "HEAT_LOAD_NOF4",
            "HEAT_LOAD_NOF5",
            "HEAT_LOAD_RTF1",
            "HEAT_LOAD_RTF2",
            # "HEAT_LOAD_SF",
            "TEMP_PH",
            "TEMP_NOF1",
            "TEMP_NOF2",
            "TEMP_NOF3",
            "TEMP_NOF4",
            "TEMP_NOF5",
            "TEMP_RTF1",
            "TEMP_RTF2",
            # "TEMP_SF",
            "STRIP_TEMP_NOF",
            "STRIP_TEMP_RTF",
            # "STRIP_TEMP_SF",
        ]
    )
    x_future_columns: list[str] = field(  # seq2seq模型输入
        default_factory=lambda: [
            # "STRIP_NAME",
            # "STRIP_TYPE",
            "STRIP_WIDTH",
            "STRIP_THICK",
            # "WELD",
            "SPEED",
            "HEAT_LOAD_NOF1",
            "HEAT_LOAD_NOF2",
            "HEAT_LOAD_NOF3",
            "HEAT_LOAD_NOF4",
            "HEAT_LOAD_NOF5",
            "HEAT_LOAD_RTF1",
            "HEAT_LOAD_RTF2",
            # "HEAT_LOAD_SF",
        ]
    )
    y_future_columns: list[str] = field(  # seq2seq模型输出
        default_factory=lambda: [
            # "TEMP_PH",
            # "TEMP_NOF1",
            # "TEMP_NOF2",
            # "TEMP_NOF3",
            # "TEMP_NOF4",
            # "TEMP_NOF5",
            # "TEMP_RTF1",
            # "TEMP_RTF2",
            # "TEMP_SF",
            "STRIP_TEMP_NOF",
            "STRIP_TEMP_RTF",
            # "STRIP_TEMP_SF",
        ]
    )


@dataclass
class Seq2SeqConfig:
    hidden_size: int = 128  # 隐藏层大小
    num_layers: int = 2  # 网络层数
    dropout: float = 0.1  # Dropout比率，用于防止过拟合
    bidirectional: bool = False  # 是否使用双向网络
    learning_rate: float = 1e-3  # 学习率
    weight_decay: float = 1e-5  # 权重衰减，用于正则化
    loss_type: str = "mse"  # 损失函数类型: mse, huber, smooth_l1
    scheduler_type: str = "plateau"  # 学习率调度器类型: plateau, cosine
    attention_type: str = "additive"  # 注意力类型: additive, multiplicative, multi_head
    use_teacher_forcing: bool = True  # 是否使用teacher forcing
    teacher_forcing_ratio: float = 0.5  # teacher forcing使用比例
    warmup_epochs: int = 5  # 学习率预热轮数


@dataclass
class AnnealingFurnaceConfig:
    total_length: float = 171.1  # 炉体总长, m
    ph_length: float = 23.08
    nof_length: float = 30.42
    rtf_length: float = 37.7
    sf_length: float = 35.48
    jcf_length: float = 44.42
    weld1_max: float = 1200  # 焊缝位置1（距焊机）最大值
    weld2_max: float = 518  # 焊缝位置2（距入炉密封辊）最大值


@dataclass
class InfluxDBCofing:
    host: str = "127.0.0.1"
    port: int = 8086
    username: str = "root"
    password: str = "123456"
    database: str = "annealing_furnace"


@dataclass
class DBConfig:
    # 默认为本地 SQLite 数据库
    db_type: str = "sqlite"  # 支持 sqlite, mysql, postgresql, mssql, oracle
    db_name: str = "app.db"  # 对SQLite是文件名，对其他数据库是数据库名
    # Common for other DBs
    username: str = ""
    password: str = ""
    host: str = "localhost"
    port: int = 3306

    echo: bool = False  # SQLAlchemy Engine配置，是否打印SQL语句
    connect_args: Dict[str, Any] = field(default_factory=dict)  # 为 connect_args 提供一个默认的空字典

    def get_db_url(self) -> str:
        """根据配置生成数据库连接 URL"""
        if self.db_type == "sqlite":
            # 对于 SQLite，connect_args 很重要
            self.connect_args = {"check_same_thread": False}
            return f"sqlite:///{self.db_name}"
        elif self.db_type == "mysql":
            # 需要安装 mysqlclient: pip install mysqlclient
            driver = "mysql+mysqlclient"
            return f"{driver}://{self.username}:{self.password}@{self.host}:{self.port}/{self.db_name}"
        elif self.db_type == "postgresql":
            # 需要安装 psycopg2: pip install psycopg2-binary
            driver = "postgresql+psycopg2"
            return f"{driver}://{self.username}:{self.password}@{self.host}:{self.port}/{self.db_name}"
        elif self.db_type == "mssql":
            # 需要安装 pyodbc: pip install pyodbc
            driver = "mssql+pyodbc"
            # 注意：MSSQL 的连接字符串通常使用 DSN
            return f"{driver}://{self.username}:{self.password}@{self.host}"  # DSN name in host
        elif self.db_type == "oracle":
            # 需要安装驱动: pip install cx_Oracle
            driver = "oracle+cx_oracle"
            dsn = f"{self.host}:{self.port}/{self.db_name}"
            return f"{driver}://{self.username}:{self.password}@{dsn}"
        else:
            raise ValueError(f"Unsupported database type: {self.db_type}")


@dataclass
class MQTTConfig:
    broker: str = "127.0.0.1"
    port: int = 1883
    keep_alive: int = 60
    user_name: str = "admin"
    password: str = "Cisdi@123456"
    # topic: str | list[str] = "test/annealing_furnace"
    topic: str = "test/annealing_furnace"


@dataclass
class LoggingConfig:
    log_dir: str = "logs"
    file_name: str = "app.log"
    level: str = "INFO"
    format: str = "[%(asctime)s] - [%(name)s] - [%(levelname)s] - %(message)s"
    date_format: str = "%Y-%m-%d %H:%M:%S"
    file_handler: bool = True
    console_handler: bool = True
    max_days: int = 30


class Configs:
    _TrainerConfig = None
    _Seq2SeqConfig = None

    _AFConfig = None
    _LoggingConfig = None
    _InfluxDBCofing = None
    _DBConfig = None
    _MQTTConfig = None

    @classmethod
    def initialize(cls, config_path_str: str = _CONFIG_FILE_PATH):
        config_path = Path(config_path_str)
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        try:
            with open(_CONFIG_FILE_PATH, "rb") as f:
                config = tomllib.load(f)
                cls._TrainerConfig = TrainerConfig(**config["trainer"])
                cls._AFConfig = AnnealingFurnaceConfig(**config["furnace"])
                cls._LoggingConfig = LoggingConfig(**config["logging"])
                cls._InfluxDBCofing = InfluxDBCofing(**config["influxdb"])
                cls._DBConfig = DBConfig(**config["database"])
                cls._MQTTConfig = MQTTConfig(**config["mqtt"])

                cls._Seq2SeqConfig = Seq2SeqConfig(**config["seq2seq"])

        except Exception as e:
            raise ValueError(f"加载配置文件时出错: {e}")

    @classmethod
    def get_train_config(cls) -> TrainerConfig:
        return cls._TrainerConfig or TrainerConfig()

    @classmethod
    def get_af_config(cls) -> AnnealingFurnaceConfig:
        return cls._AFConfig or AnnealingFurnaceConfig()

    @classmethod
    def get_logging_config(cls) -> LoggingConfig:
        return cls._LoggingConfig or LoggingConfig()

    @classmethod
    def get_influx_db_config(cls) -> InfluxDBCofing:
        return cls._InfluxDBCofing or InfluxDBCofing()

    @classmethod
    def get_db_config(cls) -> DBConfig:
        return cls._DBConfig or DBConfig()

    @classmethod
    def get_mqtt_config(cls) -> MQTTConfig:
        return cls._MQTTConfig or MQTTConfig()

    @classmethod
    def get_seq2seq_config(cls) -> Seq2SeqConfig:
        return cls._Seq2SeqConfig or Seq2SeqConfig()
