import logging
import os
from copy import deepcopy
from dataclasses import asdict
from datetime import datetime
from pathlib import Path
from typing import Optional

import pandas as pd
from sqlalchemy.orm import Session

import src.database.curd as curd
from src.config import Configs
from src.consts import FileNameEnum as FNE
from src.database import get_db_manager
from src.database.models import StripInfo, StripTracking
from src.database.schemas import StripInfoBase, StripTrackingBase
from src.utils import timer

from .track import Strip, TrackingMsg


class StripTracker:
    def __init__(self, online_mode: bool, work_dir: str):
        """
        钢卷跟踪
        在线模式下，钢卷跟踪信息会写入数据库
        离线模式下，钢卷跟踪信息会写入csv文件

        online_mode: 在线模式，会将跟踪信息写入数据库
        work_dir: 工作目录，在线模式下无效
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.online_mode = online_mode  # 在线模式，会将跟踪信息写入数据库
        self.work_dir = work_dir
        self.config = Configs.get_af_config()
        self.train_config = Configs.get_train_config()
        self.dbm = None

        # 跟踪字典
        self.strips: dict[str, Strip] = {}
        self.out_strips: list[Strip] = []

        self.loading: str = ""
        self.heating: str = ""
        self.last_loading: str = ""
        self.last_heating: str = ""
        self.next_loading: str = ""  # 下一块上料

        self.last_timestamp: Optional[datetime] = None
        self.last_selected = False  # 上次上料钢卷 False-1 True-2
        self.last_speed = float(-1)
        self.last_weld1 = float(-1)
        self.last_weld2 = float(-1)

        # 未来状态估计
        self.total_seconds = int((pd.to_timedelta(self.train_config.freq) * self.train_config.output_len).total_seconds())
        self.columns = self.train_config.x_future_columns

        if self.online_mode:
            self.dbm = get_db_manager()
            self.initialize()

    def initialize(self):
        try:
            if self.dbm is None:
                self.logger.warning("数据库未初始化，无法进行在线模式")
                return

            with self.dbm.db_session_scope() as session:
                self._get_tracking_from_db(session)

                if self.loading.strip() != "":
                    self.strips[self.loading] = self._get_strip_from_db(self.loading, session)

                if self.heating.strip() != "":
                    self.strips[self.heating] = self._get_strip_from_db(self.heating, session)

                if self.last_loading.strip() != "":
                    self.strips[self.last_loading] = self._get_strip_from_db(self.last_loading, session)

                if self.last_heating.strip() != "":
                    self.strips[self.last_heating] = self._get_strip_from_db(self.last_heating, session)

                if self.next_loading.strip() != "":
                    self.strips[self.next_loading] = self._get_strip_from_db(self.next_loading, session)

        except Exception as ex:
            self.logger.error(f"初始化失败: {ex}")
            raise ex

    def update(self, msg: TrackingMsg):
        try:
            timestamp = msg.timestamp
            selected = msg.selected
            strip1 = msg.strip1
            strip2 = msg.strip2
            speed = msg.speed
            weld1 = msg.weld1
            weld2 = msg.weld2

            # 初始化
            if self.last_timestamp is None:
                self.loading = strip1.name.strip() if not selected else strip2.name.strip()  # 更新loading钢卷号
                self.logger.info(f"[初始化]-时间戳: {timestamp}, 上料钢卷: {self.loading}")

                self.last_timestamp = timestamp
                self.last_selected = selected
                self.last_speed = speed
                self.last_weld1 = weld1
                self.last_weld2 = weld2

                return

            if self.last_timestamp >= timestamp:
                self.logger.info(f"时间戳异常，此条数据将被丢弃, last_timestamp: {self.last_timestamp}, current timestamp: {timestamp}")
                return

            # 检查并记录钢卷
            self._add_strips(strip1)
            self._add_strips(strip2)

            # 钢卷实例
            loading_strip = self._get_strip(self.loading)
            last_loading_strip = self._get_strip(self.last_loading)
            heating_strip = self._get_strip(self.heating)
            last_heating_strip = self._get_strip(self.last_heating)

            # 上料判断
            # TODO: 已上料钢卷-deque
            loading_update = False
            ready_loading = strip1.name.strip() if not selected else strip2.name.strip()
            if self.last_selected != selected or ready_loading != self.loading:  # selected 变更或上料批号更改 触发上料
                self.last_loading = self.loading  # 更新上次上料钢卷号
                self.loading = ready_loading  # 更新上料钢卷号

                loading_strip = self._get_strip(self.loading)  # 新上料钢卷实例
                last_loading_strip = self._get_strip(self.last_loading)  # 上次上料钢卷实例

                if loading_strip is not None and self.loading != self.last_loading:
                    loading_strip.loading_start_time = timestamp  # 更新上料开始时间

                if last_loading_strip is not None and self.loading != self.last_loading:
                    last_loading_strip.loading_end_time = timestamp  # 更新上料结束时间

                self.logger.info(f"[1-上料]-上料时间: {timestamp}, 上料钢卷: {self.loading}, 上料结束钢卷: {self.last_loading}")
                loading_update = True

            # 入炉判断
            heating_update = False
            if (self.last_weld2 >= self.config.weld2_max - 30 or self.last_weld2 == 0) and (weld2 > 0 and weld2 < 50):
                self.last_heating = self.heating  # 更新入炉钢卷号
                self.heating = self.loading

                last_heating_strip = self._get_strip(self.last_heating)
                heating_strip = self._get_strip(self.heating)

                if last_heating_strip is not None and self.heating != self.last_heating:
                    last_heating_strip.tail_weld_in_time = timestamp  # 更新出炉时间

                if heating_strip is not None and self.heating != self.last_heating:
                    heating_strip.head_weld_in_time = timestamp  # 更新入炉时间

                self.logger.info(f"[2-入炉]-入炉时间: {timestamp}, 入炉钢卷: {self.heating}, 入炉结束钢卷: {self.last_heating}")
                heating_update = True

            # 更新速度
            if loading_strip is not None:
                loading_strip.speed = speed
            if last_loading_strip is not None:
                last_loading_strip.speed = speed
            if heating_strip is not None:
                heating_strip.speed = speed
            if last_heating_strip is not None:
                last_heating_strip.speed = speed

            # 更新焊缝位置
            delta_t = (timestamp - self.last_timestamp).total_seconds()
            speed_s = speed / 60
            distance = speed_s * delta_t

            if loading_update:
                # 上料更新，新上料钢卷位置初始化为0
                if loading_strip is not None:
                    loading_strip.weld1 = 0
                # 更新上次上料钢卷焊缝位置
                if last_loading_strip is not None:
                    last_loading_strip.weld1 += distance
            else:
                if loading_strip is not None:
                    if weld1 > self.last_weld1:
                        loading_strip.weld1 = weld1
                    else:
                        loading_strip.weld1 += distance
                if last_loading_strip is not None:
                    last_loading_strip.weld1 += distance

            if heating_update:
                # 入炉更新，新入炉钢卷位置初始化为0
                if heating_strip is not None:
                    heating_strip.weld2 = weld2
                # 更新上次入炉钢卷焊缝位置
                if last_heating_strip is not None:
                    last_heating_strip.weld2 += distance
            else:
                if heating_strip is not None:
                    if weld2 > self.last_weld2:
                        heating_strip.weld2 = weld2
                    else:
                        heating_strip.weld2 += distance
                if last_heating_strip is not None:
                    last_heating_strip.weld2 += distance

            # 完全出炉判断
            # if heating_strip is not None and last_heating_strip is not None and heating_strip.weld2 >= self.config.total_length:
            if self.last_weld2 < self.config.total_length and weld2 >= self.config.total_length:
                self.logger.info(f"[3-出炉]-出炉时间: {timestamp}, 完全出炉钢卷: {self.last_heating}")

                if last_heating_strip is not None and self.last_heating != self.heating:
                    last_heating_strip.tail_weld_out_time = timestamp
                    self.out_strips.append(last_heating_strip)
                    self._del_strip(self.last_heating)

                if heating_strip is not None and self.heating != self.last_heating:
                    heating_strip.head_weld_out_time = timestamp

            # 更新时间戳
            self.last_timestamp = timestamp
            self.last_selected = selected
            self.last_speed = speed
            self.last_weld1 = weld1
            self.last_weld2 = weld2

            if self.online_mode:
                self._update_to_db()

        except Exception as e:
            self.logger.error(f"更新钢卷跟踪失败: {e}")

    def get_heating_strip(self) -> Optional[Strip]:
        return self._get_strip(self.heating)

    def get_loading_strip(self) -> Optional[Strip]:
        return self._get_strip(self.loading)

    def get_next_heating_strip(self) -> Optional[Strip]:
        if self.heating != self.loading:
            return self._get_strip(self.loading)
        return self._get_strip(self.next_loading)

    def _add_strips(self, strip: Strip) -> bool:
        if strip.name is None or strip.name.strip() == "":
            return False

        if strip.name in self.strips:  # 已存在的钢卷更新信息，不新增钢卷实例
            if not self.strips[strip.name].check_strip_info():
                self.strips[strip.name].update_strip_info(strip.type, strip.width, strip.thick, strip.length, strip.weight)
            return False

        self.strips[strip.name] = strip

        if strip.name != self.loading:
            self.next_loading = strip.name  # 更新下一块上料钢卷号

        return True

    def _get_strip(self, key: str) -> Optional[Strip]:
        if key is None or key.strip() == "":
            return None

        return self.strips.get(key, None)

    def _del_strip(self, key: str) -> bool:
        if key not in self.strips:
            return False
        del self.strips[key]
        return True

    def _get_tracking_from_db(self, session: Session):
        strip_tracking: Optional[StripTracking] = curd.get_strip_status(session)

        if strip_tracking is not None:
            self.loading = strip_tracking.LOADING_NAME
            self.heating = strip_tracking.HEATING_NAME
            self.last_loading = strip_tracking.LAST_LOADING_NAME
            self.last_heating = strip_tracking.LAST_HEATING_NAME
            self.last_timestamp = strip_tracking.TIMESTAMP
            self.last_selected = strip_tracking.SELECTED
            self.last_speed = strip_tracking.SPEED
            self.last_weld1 = strip_tracking.WELD1
            self.last_weld2 = strip_tracking.WELD2

    def _get_strip_from_db(self, strip_name: str, session: Session) -> Strip:
        strip_info: Optional[StripInfo] = curd.get_strip_info_by_name(session, strip_name)
        if strip_info is None:
            return Strip(name=strip_name)

        return Strip(
            name=strip_info.STRIP_NAME,
            type=strip_info.STRIP_TYPE,
            width=strip_info.STRIP_WIDTH,
            thick=strip_info.STRIP_THICK,
            length=strip_info.STRIP_LENGTH,
            weight=strip_info.STRIP_WEIGHT,
            weld1=strip_info.WELD1,
            weld2=strip_info.WELD2,
            speed=strip_info.SPEED,
            loading_start_time=strip_info.LOADING_START_TIME,
            loading_end_time=strip_info.LOADING_END_TIME,
            head_weld_in_time=strip_info.HEAD_WELD_IN_TIME,
            head_weld_out_time=strip_info.HEAD_WELD_OUT_TIME,
            tail_weld_in_time=strip_info.TAIL_WELD_IN_TIME,
            tail_weld_out_time=strip_info.TAIL_WELD_OUT_TIME,
        )

    def _update_to_db(self):
        if self.dbm is None:
            return

        with self.dbm.db_session_scope() as session:
            self._update_tracking_to_db(session)

            for strip in self.strips.values():
                self._update_strip_info_to_db(session, strip)

            for strip in self.out_strips:
                succeed = self._update_strip_info_to_db(session, strip)
                if succeed:
                    self.out_strips.remove(strip)

    def _update_tracking_to_db(self, session: Session):
        try:
            tracking = StripTrackingBase(
                LOADING_NAME=self.loading.strip(),
                HEATING_NAME=self.heating.strip(),
                LAST_LOADING_NAME=self.last_loading.strip(),
                LAST_HEATING_NAME=self.last_heating.strip(),
                NEXT_LOADING_NAME=self.next_loading.strip(),
                SELECTED=self.last_selected,
                SPEED=self.last_speed,
                WELD1=self.last_weld1,
                WELD2=self.last_weld2,
                TIMESTAMP=self.last_timestamp,
            )
            curd.update_or_create_strip_status(session, tracking)
        except Exception as e:
            self.logger.error(f"更新跟踪状态失败: {e}")

    def _update_strip_info_to_db(self, session: Session, strip: Strip) -> bool:
        try:
            if strip.name is None or strip.name.strip() == "":
                return False

            strip_info = StripInfoBase(
                STRIP_NAME=strip.name,
                STRIP_TYPE=strip.type,
                STRIP_WIDTH=strip.width,
                STRIP_THICK=strip.thick,
                STRIP_LENGTH=strip.length,
                STRIP_WEIGHT=strip.weight,
                WELD1=strip.weld1,
                WELD2=strip.weld2,
                SPEED=strip.speed,
                LOADING_START_TIME=strip.loading_start_time,
                LOADING_END_TIME=strip.loading_end_time,
                HEAD_WELD_IN_TIME=strip.head_weld_in_time,
                HEAD_WELD_OUT_TIME=strip.head_weld_out_time,
                TAIL_WELD_IN_TIME=strip.tail_weld_in_time,
                TAIL_WELD_OUT_TIME=strip.tail_weld_out_time,
            )
            curd.update_or_create_strip_info(session, strip_info)
            return True
        except Exception as e:
            self.logger.error(f"更新钢卷信息失败: {e}")
            return False

    def save_tracking_data(self):
        try:
            if self.work_dir is None or self.work_dir.strip() == "":
                self.logger.warning("保存跟踪数据失败，工作目录为空")
                return

            os.makedirs(self.work_dir, exist_ok=True)  # 创建工作目录，如果不存在的话
            df = pd.DataFrame([asdict(strip) for strip in self.out_strips])
            df.to_csv(Path(self.work_dir, f"{FNE.TRACK_HIS.value}.csv"), index=False)
        except Exception as e:
            self.logger.error(f"保存跟踪数据失败: {e}")

    @timer
    def get_future_data(self) -> Optional[pd.DataFrame]:
        """
        根据当前产线速度，获取未来时间窗口的跟踪数据
        """
        try:
            last_time = deepcopy(self.last_timestamp)
            speed = self.last_speed
            speed_s = speed / 60
            heating_strip = self.get_heating_strip()
            next_strip = self.get_next_heating_strip()

            if heating_strip is not None:
                heating_strip = deepcopy(heating_strip)
            else:
                self.logger.warning("正在加热钢卷为空，无法估计未来数据")
                return None

            if next_strip is not None:
                next_strip = deepcopy(next_strip)
            else:
                self.logger.warning("下一块上料钢卷为空，使用正在加热钢卷代替")
                next_strip = heating_strip

            widths, thicks, speeds = [], [], []
            current_strip = heating_strip
            current_weld_pos = heating_strip.weld2
            self.logger.info(f"[跟踪数据估计]-开始模拟跟踪，正在加热钢卷: {current_strip.name} 焊缝起始位置: {current_weld_pos}m")
            for _ in range(self.total_seconds):
                if current_weld_pos > current_strip.length:
                    current_weld_pos -= current_strip.length
                    current_strip = next_strip
                    self.logger.info(f"[跟踪数据估计]-模拟跟踪，切换钢卷: {current_strip.name}, 新焊缝位置: {current_weld_pos:.2f}m")

                # 将当前秒的数据追加到列表
                widths.append(current_strip.width)
                thicks.append(current_strip.thick)
                speeds.append(speed)

                # 更新下一秒的焊缝位置 (每秒前进 speed_s 米)
                current_weld_pos += speed_s
                self.logger.debug(f"[跟踪数据估计]-模拟跟踪，焊缝位置: {current_weld_pos}m")

            # 初始化DataFrame
            date_index = pd.date_range(start=last_time, periods=self.total_seconds, freq="S")
            df = pd.DataFrame(
                {
                    "STRIP_WIDTH": widths,
                    "STRIP_THICK": thicks,
                    "SPEED": speeds,
                },
                index=date_index,
            )
            return df
        except Exception as e:
            self.logger.error(f"获取预测数据失败: {e}")
            return None
