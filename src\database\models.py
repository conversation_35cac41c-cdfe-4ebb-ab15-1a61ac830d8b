from datetime import datetime
from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, Float, Integer, String, func
from sqlalchemy.orm import Mapped, mapped_column

from .database import Base


class StripInfo(Base):
    """
    钢卷信息表 (Strip Info) - 使用 SQLAlchemy 2.0 风格，类型安全
    """

    __tablename__ = "strip_info"

    # 语法: attribute_name: Mapped[python_type] = mapped_column(sqlalchemy_type, ...)

    # 对于非空字段
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    STRIP_NAME: Mapped[str] = mapped_column(String(255), unique=True, index=True)
    STRIP_TYPE: Mapped[str] = mapped_column(String(100), nullable=False, comment="钢卷类型")
    STRIP_WIDTH: Mapped[float] = mapped_column(Float, nullable=False, comment="钢卷宽度 (mm)")
    STRIP_THICK: Mapped[float] = mapped_column(Float, nullable=False, comment="钢卷厚度 (mm)")
    STRIP_LENGTH: Mapped[float] = mapped_column(Float, nullable=False, comment="钢卷长度 (m)")
    STRIP_WEIGHT: Mapped[float] = mapped_column(Float, nullable=False, comment="钢卷重量 (kg)")
    WELD1: Mapped[float] = mapped_column(Float, nullable=False, comment="焊缝1位置")
    WELD2: Mapped[float] = mapped_column(Float, nullable=False, comment="焊缝2位置")
    SPEED: Mapped[float] = mapped_column(Float, nullable=False, comment="速度 (m/min)")
    # DateTime 字段的类型安全写法
    LOADING_START_TIME: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="上料开始时间")
    LOADING_END_TIME: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="上料结束时间")
    HEAD_WELD_IN_TIME: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="头部焊缝入炉时间")
    HEAD_WELD_OUT_TIME: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="头部焊缝出炉时间")
    TAIL_WELD_IN_TIME: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="尾部焊缝入炉时间")
    TAIL_WELD_OUT_TIME: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, comment="尾部焊缝出炉时间")

    def __repr__(self):
        return f"<StripInfo(id={self.id}, STRIP_NAME='{self.STRIP_NAME}')>"


class StripTracking(Base):
    """
    钢卷跟踪信息表 (Strip Tracking) - 使用 SQLAlchemy 2.0 风格，类型安全
    """

    __tablename__ = "stirp_tracking"

    # 针对单行状态表的 id 定义
    id: Mapped[int] = mapped_column(Integer, primary_key=True, comment="固定的主键ID (例如: 1)，用于标识唯一的跟踪状态行")

    LOADING_NAME: Mapped[str] = mapped_column(String(255), nullable=False, default="", comment="当前上料钢卷")
    HEATING_NAME: Mapped[str] = mapped_column(String(255), nullable=False, default="", comment="当前入炉钢卷")
    LAST_LOADING_NAME: Mapped[str] = mapped_column(String(255), nullable=False, default="", comment="上次上料钢卷")
    LAST_HEATING_NAME: Mapped[str] = mapped_column(String(255), nullable=False, default="", comment="上次入炉钢卷")
    NEXT_LOADING_NAME: Mapped[str] = mapped_column(String(255), nullable=False, default="", comment="下个上料钢卷")

    SELECTED: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False, comment="是否被选中 (0: 未选中, 1: 已选中)")

    SPEED: Mapped[float] = mapped_column(Float, nullable=False, default=0.0, comment="当前速度 (m/min)")
    WELD1: Mapped[float] = mapped_column(Float, nullable=False, default=0.0, comment="焊缝1位置")
    WELD2: Mapped[float] = mapped_column(Float, nullable=False, default=0.0, comment="焊缝2位置")

    TIMESTAMP: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, server_default=func.now(), comment="记录时间戳")

    def __repr__(self):
        return f"<StripTracking(id={self.id}, LOADING_NAME='{self.LOADING_NAME}')>"
