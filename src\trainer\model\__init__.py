"""
模型模块

包含各种深度学习模型的实现，包括：
- LSTM 模型
- GRU 模型
- Seq2Seq 模型
- 带注意力机制的模型
- PyTorch Lightning 包装类
"""

# Seq2Seq 相关模型
from .seq2seq import Seq2Seq, Encoder, Decoder
from .seq2seq_attention import (
    Seq2SeqWithAttention,
    DecoderWithAttention,
    Encoder,
    PositionalEncoding,
    MultiHeadAttention,
    ImprovedAttention,
)

# Lightning 包装类
from .lit_seq2seq import LitSeq2Seq
from .lit_seq2seq_attention import LitSeq2SeqWithAttention

__all__ = [
    # Seq2Seq 基础模型
    "Seq2Seq",
    "Encoder",
    "Decoder",
    # Seq2Seq 注意力模型
    "Seq2SeqWithAttention",
    "DecoderWithAttention",
    # Lightning 包装类
    "LitSeq2Seq",
    "LitSeq2SeqWithAttention",
]
