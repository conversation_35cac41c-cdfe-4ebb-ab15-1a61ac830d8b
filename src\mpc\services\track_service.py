import logging
from multiprocessing import Queue
from typing import Optional

from src.config.configs import Configs
from src.config.logger import Logger
from src.consts import LogFileNameEnum as LFN
from src.database.database import get_db_manager, init_database
from src.track import Strip, TrackingMsg
from src.track.strip_tracker import StripTracker


######################
# 已弃用
######################
class TrackService:
    def __init__(self, input_queue: Queue, output_queue: Queue):
        Configs.initialize()
        Logger.initialize(file_name=LFN.MPC_TRACK.value)

        self.logger = logging.getLogger(self.__class__.__name__)
        self.input_queue = input_queue
        self.output_queue = output_queue

        # 初始化数据库
        init_database(Configs.get_db_config())
        self.dbm = get_db_manager()
        self.dbm.create_tables()

        self.strip_tracker = StripTracker(online_mode=True, work_dir="")

    def start(self):
        self.logger.info("跟踪服务启动中...")

        while True:
            try:
                record = self.input_queue.get()
                self.logger.info(f"收到消息：{record}")

                # TODO: 跟踪处理
                timestamp = record.get("TIME_STAMP")
                selected = record.get("SELECTED")
                strip1 = Strip(
                    name=record.get("STRIP_NAME_1"),
                    type=record.get("STRIP_TYPE_1"),
                    width=record.get("STRIP_WIDTH_1"),
                    thick=record.get("STRIP_THICK_1"),
                    length=record.get("STRIP_LENGTH_1"),
                    weight=record.get("STRIP_WEIGHT_1"),
                )
                strip2 = Strip(
                    name=record.get("STRIP_NAME_2"),
                    type=record.get("STRIP_TYPE_2"),
                    width=record.get("STRIP_WIDTH_2"),
                    thick=record.get("STRIP_THICK_2"),
                    length=record.get("STRIP_LENGTH_2"),
                    weight=record.get("STRIP_WEIGHT_2"),
                )
                track_msg = TrackingMsg(
                    timestamp=timestamp,
                    selected=selected,
                    strip1=strip1,
                    strip2=strip2,
                    weld1=record.get("WELD_POSITION_1"),
                    weld2=record.get("WELD_POSITION_2"),
                    speed=record.get("SPEED"),
                )
                self.strip_tracker.update(track_msg)

                heating_strip: Optional[Strip] = self.strip_tracker.get_heating_strip()

                if heating_strip is not None:
                    record["STRIP_NAME"] = heating_strip.name
                    record["STRIP_TYPE"] = heating_strip.type
                    record["STRIP_WIDTH"] = heating_strip.width
                    record["STRIP_THICK"] = heating_strip.thick
                    record["STRIP_LENGTH"] = heating_strip.length
                    record["STRIP_WEIGHT"] = heating_strip.weight
                    record["WELD"] = heating_strip.weld2

                    self.output_queue.put(record)  # 将处理后的记录放入输出队列
                else:
                    self.logger.warning("跟踪失败...")
            except KeyboardInterrupt:
                self.logger.warning("收到中断信号，正在停止服务...")
                break
            except Exception as ex:
                self.logger.error(f"消息处理失败：{ex}")
