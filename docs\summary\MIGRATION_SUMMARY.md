# 元数据迁移总结

本文档总结了从 setup.py 到 pyproject.toml 的元数据迁移过程，以遵循现代 Python 打包标准（PEP 621）。

## 迁移的元数据

### 从 setup.py 迁移到 pyproject.toml 的内容：

1. **项目基本信息**
   - `name`: "model-predictive-control"
   - `version`: "1.0.0" (从 "0.1.0" 更新)
   - `description`: "Model Predictive Control for Annealing Furnace"
   - `readme`: "README.md"
   - `requires-python`: ">=3.12"

2. **作者信息**
   - `authors`: [{ name = "CISDI" }]

3. **许可证信息**
   - `license`: "MIT" (使用现代 SPDX 表达式)

4. **分类器 (classifiers)**
   - 开发状态、目标受众、主题、编程语言、操作系统等
   - 移除了过时的许可证分类器，使用现代 SPDX 表达式

5. **依赖关系**
   - `dependencies`: 所有运行时依赖
   - 保持与原 setup.py 完全一致

6. **控制台脚本**
   - `[project.scripts]` 部分
   - `afmpc-trainer` 和 `afmpc-mpc` 入口点

7. **构建系统配置**
   - `[build-system]`: 指定使用 setuptools 作为构建后端
   - `[tool.setuptools.packages.find]`: 包发现配置

## 保留在 setup.py 中的内容

setup.py 现在只保留了无法在 pyproject.toml 中声明的内容：

1. **自定义命令类**
   - `BuildExecutablesCommand`: Nuitka 构建命令
   - `cmdclass` 配置

2. **Nuitka 配置**
   - `NUITKA_OPTIONS`: Nuitka 打包参数
   - `NuitkaBuildCommand`: 基础构建命令类

## 迁移前后对比

### 迁移前的 setup.py (56 行元数据)
```python
setup(
    name="model-predictive-control",
    version="1.0.0",
    description="Model Predictive Control for Annealing Furnace",
    long_description=read_file("README.md"),
    long_description_content_type="text/markdown",
    author="CISDI",
    packages=find_packages(),
    python_requires=">=3.12",
    install_requires=[...],  # 23 个依赖
    entry_points={...},
    cmdclass={...},
    classifiers=[...],  # 7 个分类器
)
```

### 迁移后的 setup.py (5 行)
```python
setup(
    cmdclass={
        "build_exe": BuildExecutablesCommand,
    },
)
```

### pyproject.toml 新增内容
```toml
[project]
# 所有项目元数据现在都在这里

[project.scripts]
# 控制台脚本入口点

[build-system]
# 构建系统配置

[tool.setuptools.packages.find]
# 包发现配置
```

## 优势

1. **符合现代标准**: 遵循 PEP 621 标准
2. **声明式配置**: 元数据以声明式方式定义，更清晰
3. **工具兼容性**: 更好地与现代 Python 工具（如 pip、build、twine）集成
4. **减少重复**: 消除了 setup.py 和 pyproject.toml 之间的重复配置
5. **简化维护**: setup.py 现在只关注自定义逻辑，元数据集中管理

## 验证

迁移后的配置已通过以下测试：
- ✅ `python setup.py build_exe --help` 正常工作
- ✅ 无 setuptools 警告或错误
- ✅ 所有元数据正确读取
- ✅ 自定义命令功能完整保留

## 注意事项

1. **向后兼容**: 迁移保持了所有原有功能
2. **版本要求**: 需要 setuptools >= 61.0 支持 pyproject.toml
3. **工具支持**: 现代 Python 打包工具都支持这种配置方式
