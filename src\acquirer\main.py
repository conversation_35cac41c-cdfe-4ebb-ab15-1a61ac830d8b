import logging
import sys
import time
from datetime import datetime

from src.acquirer import MQTTDataSimulator, SimulatorConfig
from src.config import Configs, Logger
from src.consts import LogFileNameEnum as LFN


def acquirer_start():
    Configs.initialize()
    Logger.initialize(file_name=LFN.ACQUIRE.value)
    logger = logging.getLogger(__name__)

    try:
        simulator_config = SimulatorConfig()
        influx_config = Configs.get_influx_db_config()
        mqtt_config = Configs.get_mqtt_config()

        start_time = datetime(2025, 5, 1, 0, 0, 0)
        end_time = datetime(2025, 6, 30, 23, 59, 59)

        # 创建模拟器实例
        simulator = MQTTDataSimulator(
            influx_config=influx_config,
            mqtt_config=mqtt_config,
            simulator_config=simulator_config,
            start_time=start_time,
            end_time=end_time,
        )

        logger.info("启动模拟器...")

        # 使用上下文管理器确保资源正确释放
        with simulator:
            logger.info("模拟器已启动，开始发送数据...")

            # 定期输出状态信息
            while simulator._running:
                time.sleep(10)  # 每10秒输出一次状态
                # status = simulator.get_status()

        logger.info("模拟器示例运行完成")
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在停止...")
    except Exception as e:
        logger.error(f"运行模拟器时发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    acquirer_start()
