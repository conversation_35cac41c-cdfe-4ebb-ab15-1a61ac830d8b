from datetime import datetime
from typing import Optional

from pydantic import BaseModel


class StripInfoBase(BaseModel):
    STRIP_NAME: str
    STRIP_TYPE: str
    STRIP_WIDTH: float
    STRIP_THICK: float
    STRIP_LENGTH: float
    STRIP_WEIGHT: float
    WELD1: float
    WELD2: float
    SPEED: float
    LOADING_START_TIME: Optional[datetime] = None
    LOADING_END_TIME: Optional[datetime] = None
    HEAD_WELD_IN_TIME: Optional[datetime] = None
    HEAD_WELD_OUT_TIME: Optional[datetime] = None
    TAIL_WELD_IN_TIME: Optional[datetime] = None
    TAIL_WELD_OUT_TIME: Optional[datetime] = None


class StripTrackingBase(BaseModel):
    LOADING_NAME: str
    HEATING_NAME: str
    LAST_LOADING_NAME: str
    LAST_HEATING_NAME: str
    NEXT_LOADING_NAME: str  # 下一块上料钢卷号，用于预测下一块上料的速度
    SELECTED: bool
    SPEED: float
    WELD1: float
    WELD2: float
    TIMESTAMP: Optional[datetime] = None
