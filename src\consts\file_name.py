import enum


class FileNameEnum(enum.Enum):
    TRACK_HIS = "data_track_history"
    TRACKED = "data_tracked"
    SOURCE = "data_source"
    SORTED = "data_sorted"
    INDEXED = "data_indexed"
    ANOMALY_REMOVED = "data_anomaly_removed"
    FILLED = "data_filled"
    RESAMPLED = "data_resampled"
    FINAL = "data_final"
    SEGMENT = "data_segment"
    TRAIN = "data_train"
    VALID = "data_valid"
    TEST = "data_test"

    # meta
    NORMALIZE_META = "normalize_meta"
    TRAIN_CONFIG_META = "train_config_meta"
    MODEL_CONFIG_META = "model_config_meta"

    # model name
    SEQ2SEQ_NAME = "seq2seq"


class LogFileNameEnum(enum.Enum):
    MPC_MQTT = "mpc/mqtt.log"
    MPC_TRACK = "mpc/track.log"
    MPC_STATE = "mpc/state.log"
    MPC_SERVICE = "mpc/mpc.log"
    ACQUIRE = "acquire/simulator.log"
    TRAIN = "train/train.log"
