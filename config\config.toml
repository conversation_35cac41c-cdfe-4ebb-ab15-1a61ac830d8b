[trainer]
save_data = true               # 是否保存训练数据数据
freq = "30s"                   # 数据采样频率
min_data_length = 512          # 最小数据长度要求
split_ratio = [0.8, 0.1, 0.1]  # 训练集、验证集、测试集的划分比例 (增加训练集比例)
normalize_method = "z-score"   # 数据归一化方法 (改为z-score，更适合温度数据)
input_len = 64                 # 输入序列长度(回看窗口) (优化后的长度)
output_len = 48                # 预测序列长度(预测步长) (优化后的长度)
batch_size = 64                # 批处理大小 (适中的批次大小，平衡内存和性能)
epochs = 150                   # 训练轮数 (适度减少)
accelerator = "auto"           # 自动选择加速器（CPU/GPU）
devices = "auto"               # 自动选择设备数量
gradient_clip_val = 1.0        # 梯度裁剪值，防止梯度爆炸 (增加裁剪值)
early_stopping_patience = 20   # 早停patience，连续多少轮无改善后停止训练
accumulate_grad_batches = 2    # 梯度累积批次，有效增大批次大小

[seq2seq]
hidden_size = 128             # 隐藏层大小 (优化后的大小)
num_layers = 2                 # 网络层数 (增加层数)
dropout = 0.15                 # Dropout比率，用于防止过拟合 (适度调整)
bidirectional = false          # 是否使用双向网络
learning_rate = 5e-4           # 学习率 (降低学习率提高稳定性)
weight_decay = 5e-5            # 权重衰减，用于正则化 (减少正则化强度)
loss_type = "huber"            # 损失函数类型 (改为Huber损失，对异常值更鲁棒)
scheduler_type = "cosine"      # 学习率调度器类型 (改为余弦退火)
attention_type = "multi_head"  # 注意力类型: additive, multiplicative, multi_head
use_teacher_forcing = true     # 是否使用teacher forcing
teacher_forcing_ratio = 0.7    # teacher forcing使用比例
warmup_epochs = 5              # 学习率预热轮数

[furnace]
total_length = 171.1  # 炉体总长, m
ph_length = 23.08
nof_length = 30.42
rtf_length = 37.7
sf_length = 35.48
jcf_length = 44.42
weld1_max = 1200  # 焊缝位置1（距焊机）最大值
weld2_max = 518  # 焊缝位置2（距入炉密封辊）最大值

[influxdb]
host = "***********"
port = 8086
username = "root"
password = "123456"
database = "annealing_furnace"

[database]
db_type = "sqlite"
db_name = "db/app.db"
username = ""
password = ""
host = ""
port = 1521
echo = false

[mqtt]
broker = "**************"
port = 1883
keep_alive = 60
user_name = "admin"
password = "Cisdi@123456"
topic = "annealing_furnace/simulate"

[logging]
log_dir = "logs"
file_name = "app.log"
level = "INFO"
format = "[%(asctime)s] - [%(name)s] - [%(levelname)s] - %(message)s"
date_format = "%Y-%m-%d %H:%M:%S"
file_handler = true
console_handler = true
max_days = 30

# 新增数据预处理配置
[preprocessing]
# 异常值检测配置
outlier_detection = true
outlier_method = "iqr"         # iqr, zscore, isolation_forest
outlier_threshold = 3.0        # 异常值阈值

# 特征工程配置
feature_engineering = true
add_time_features = true       # 添加时间特征（小时、星期等）
add_lag_features = true        # 添加滞后特征
lag_periods = [1, 2, 3, 5, 10] # 滞后周期
add_rolling_features = true    # 添加滚动统计特征
rolling_windows = [5, 10, 20]  # 滚动窗口大小
add_diff_features = true       # 添加差分特征

# 数据平滑配置
smoothing = true
smoothing_method = "savgol"    # savgol, moving_average, exponential
smoothing_window = 5           # 平滑窗口大小

# 温度数据特定配置
[temperature_thresholds]
# 更合理的温度阈值设置
speed_min = 5.0                # 最小速度 (降低阈值)
strip_temp_nof_min = 400.0     # NOF段钢带温度最小值 (降低阈值)
strip_temp_nof_max = 800.0     # NOF段钢带温度最大值
strip_temp_rtf_min = 400.0     # RTF段钢带温度最小值
strip_temp_rtf_max = 750.0     # RTF段钢带温度最大值
strip_temp_sf_min = 350.0      # SF段钢带温度最小值 (降低阈值)
strip_temp_sf_max = 650.0      # SF段钢带温度最大值

# 炉温阈值
temp_ph_min = 500.0            # PH段炉温最小值
temp_ph_max = 900.0            # PH段炉温最大值
temp_nof_min = 600.0           # NOF段炉温最小值
temp_nof_max = 950.0           # NOF段炉温最大值
