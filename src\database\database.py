import logging
import threading
from contextlib import contextmanager

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from src.config.configs import DBConfig

Base = declarative_base()


class _DatabaseManager:
    """
    数据库管理器内部实现类
    """

    def __init__(self, config: DBConfig):
        self.config = config
        self.engine = None
        self.SessionLocal = None
        self._connect()

    def _connect(self):
        db_url = self.config.get_db_url()
        self.engine = create_engine(db_url, echo=self.config.echo, connect_args=self.config.connect_args)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        logging.info("Database connection established.")

    def create_tables(self):
        Base.metadata.create_all(bind=self.engine)

    @contextmanager
    def db_session_scope(self):
        if not self.SessionLocal:
            raise ConnectionError("Database not initialized. Call init_database() first.")
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()


# --- 单例实现的核心 ---

# 这个变量将保存 _DatabaseManager 的唯一实例
db_manager: _DatabaseManager | None = None
# 创建一个锁来确保线程安全
_lock = threading.Lock()


def init_database(config: DBConfig):
    """
    全局唯一的数据库初始化函数。
    只能被调用一次。
    """
    global db_manager
    # 使用双重检查锁定来确保线程安全且高效
    if db_manager is None:
        with _lock:
            if db_manager is None:
                logging.info("Initializing database manager singleton...")
                db_manager = _DatabaseManager(config)
            else:
                logging.warning("Database manager already initialized by another thread.")
    else:
        logging.warning("Database manager has already been initialized.")


def get_db_manager() -> _DatabaseManager:
    """
    获取数据库管理器的唯一实例。
    如果未初始化，则会引发错误。
    """
    if db_manager is None:
        raise ConnectionError("Database manager is not initialized. Call init_database() from your main application entry point first.")
    return db_manager
