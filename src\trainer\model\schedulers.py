import torch
import torch.nn as nn
from torch.optim.lr_scheduler import (
    ReduceLROnPlateau, 
    CosineAnnealingLR, 
    CosineAnnealingWarmRestarts,
    OneCycleLR,
    _LRScheduler
)
import math


class WarmupScheduler(_LRScheduler):
    """学习率预热调度器"""
    
    def __init__(self, optimizer, warmup_epochs, base_scheduler=None, last_epoch=-1):
        self.warmup_epochs = warmup_epochs
        self.base_scheduler = base_scheduler
        super(WarmupScheduler, self).__init__(optimizer, last_epoch)
        
    def get_lr(self):
        if self.last_epoch < self.warmup_epochs:
            # 预热阶段：线性增长
            warmup_factor = (self.last_epoch + 1) / self.warmup_epochs
            return [base_lr * warmup_factor for base_lr in self.base_lrs]
        else:
            # 预热结束后使用基础调度器
            if self.base_scheduler is not None:
                # 调整基础调度器的epoch
                self.base_scheduler.last_epoch = self.last_epoch - self.warmup_epochs
                return self.base_scheduler.get_lr()
            else:
                return self.base_lrs


class CosineAnnealingWarmupRestarts(_LRScheduler):
    """带预热的余弦退火重启调度器"""
    
    def __init__(
        self, 
        optimizer, 
        first_cycle_steps, 
        cycle_mult=1.0, 
        max_lr=0.1, 
        min_lr=0.001, 
        warmup_steps=0, 
        gamma=1.0, 
        last_epoch=-1
    ):
        self.first_cycle_steps = first_cycle_steps
        self.cycle_mult = cycle_mult
        self.max_lr = max_lr
        self.min_lr = min_lr
        self.warmup_steps = warmup_steps
        self.gamma = gamma
        
        self.cur_cycle_steps = first_cycle_steps
        self.cycle = 0
        self.step_in_cycle = last_epoch
        
        super(CosineAnnealingWarmupRestarts, self).__init__(optimizer, last_epoch)
        
        # 初始化学习率
        self.init_lr()
        
    def init_lr(self):
        self.base_lrs = []
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = self.min_lr
            self.base_lrs.append(self.min_lr)
            
    def get_lr(self):
        if self.step_in_cycle == -1:
            return self.base_lrs
        elif self.step_in_cycle < self.warmup_steps:
            return [(self.max_lr - base_lr) * self.step_in_cycle / self.warmup_steps + base_lr
                    for base_lr in self.base_lrs]
        else:
            return [base_lr + (self.max_lr - base_lr) * 
                    (1 + math.cos(math.pi * (self.step_in_cycle - self.warmup_steps) / 
                                  (self.cur_cycle_steps - self.warmup_steps))) / 2
                    for base_lr in self.base_lrs]
                    
    def step(self, epoch=None):
        if epoch is None:
            epoch = self.last_epoch + 1
            self.step_in_cycle = self.step_in_cycle + 1
            if self.step_in_cycle >= self.cur_cycle_steps:
                self.cycle += 1
                self.step_in_cycle = self.step_in_cycle - self.cur_cycle_steps
                self.cur_cycle_steps = int((self.cur_cycle_steps - self.warmup_steps) * self.cycle_mult) + self.warmup_steps
        else:
            if epoch >= self.first_cycle_steps:
                if self.cycle_mult == 1.0:
                    self.step_in_cycle = epoch % self.first_cycle_steps
                    self.cycle = epoch // self.first_cycle_steps
                else:
                    n = int(math.log((epoch / self.first_cycle_steps * (self.cycle_mult - 1) + 1), self.cycle_mult))
                    self.cycle = n
                    self.step_in_cycle = epoch - int(self.first_cycle_steps * (self.cycle_mult ** n - 1) / (self.cycle_mult - 1))
                    self.cur_cycle_steps = self.first_cycle_steps * self.cycle_mult ** (n)
            else:
                self.cur_cycle_steps = self.first_cycle_steps
                self.step_in_cycle = epoch
                
        self.max_lr = self.base_lrs[0] * (self.gamma ** self.cycle)
        self.last_epoch = math.floor(epoch)
        
        for param_group, lr in zip(self.optimizer.param_groups, self.get_lr()):
            param_group['lr'] = lr


class PolynomialLRDecay(_LRScheduler):
    """多项式学习率衰减"""
    
    def __init__(self, optimizer, max_decay_steps, end_learning_rate=0.0001, power=1.0, last_epoch=-1):
        self.max_decay_steps = max_decay_steps
        self.end_learning_rate = end_learning_rate
        self.power = power
        super(PolynomialLRDecay, self).__init__(optimizer, last_epoch)
        
    def get_lr(self):
        if self.last_epoch < self.max_decay_steps:
            return [
                (base_lr - self.end_learning_rate) * 
                ((1 - self.last_epoch / self.max_decay_steps) ** self.power) + 
                self.end_learning_rate
                for base_lr in self.base_lrs
            ]
        else:
            return [self.end_learning_rate for _ in self.base_lrs]


class ExponentialLRDecay(_LRScheduler):
    """指数学习率衰减"""
    
    def __init__(self, optimizer, decay_steps, decay_rate, staircase=False, last_epoch=-1):
        self.decay_steps = decay_steps
        self.decay_rate = decay_rate
        self.staircase = staircase
        super(ExponentialLRDecay, self).__init__(optimizer, last_epoch)
        
    def get_lr(self):
        if self.staircase:
            return [base_lr * (self.decay_rate ** (self.last_epoch // self.decay_steps))
                    for base_lr in self.base_lrs]
        else:
            return [base_lr * (self.decay_rate ** (self.last_epoch / self.decay_steps))
                    for base_lr in self.base_lrs]


class AdaptiveLRScheduler(_LRScheduler):
    """自适应学习率调度器，根据损失变化调整学习率"""
    
    def __init__(
        self, 
        optimizer, 
        patience=10, 
        factor=0.5, 
        min_lr=1e-8, 
        threshold=1e-4,
        threshold_mode='rel',
        last_epoch=-1
    ):
        self.patience = patience
        self.factor = factor
        self.min_lr = min_lr
        self.threshold = threshold
        self.threshold_mode = threshold_mode
        
        self.best_loss = None
        self.num_bad_epochs = 0
        self.loss_history = []
        
        super(AdaptiveLRScheduler, self).__init__(optimizer, last_epoch)
        
    def step(self, metrics=None):
        if metrics is None:
            super(AdaptiveLRScheduler, self).step()
            return
            
        current_loss = float(metrics)
        self.loss_history.append(current_loss)
        
        if self.best_loss is None:
            self.best_loss = current_loss
        elif self._is_better(current_loss, self.best_loss):
            self.best_loss = current_loss
            self.num_bad_epochs = 0
        else:
            self.num_bad_epochs += 1
            
        if self.num_bad_epochs > self.patience:
            self._reduce_lr()
            self.num_bad_epochs = 0
            
        super(AdaptiveLRScheduler, self).step()
        
    def _is_better(self, current, best):
        if self.threshold_mode == 'rel':
            rel_epsilon = 1.0 - self.threshold
            return current < best * rel_epsilon
        else:
            return current < best - self.threshold
            
    def _reduce_lr(self):
        for param_group in self.optimizer.param_groups:
            old_lr = float(param_group['lr'])
            new_lr = max(old_lr * self.factor, self.min_lr)
            param_group['lr'] = new_lr
            
    def get_lr(self):
        return [param_group['lr'] for param_group in self.optimizer.param_groups]


def get_scheduler(scheduler_type: str, optimizer, **kwargs):
    """获取学习率调度器的工厂函数"""
    
    if scheduler_type == "plateau":
        return ReduceLROnPlateau(
            optimizer,
            mode=kwargs.get('mode', 'min'),
            factor=kwargs.get('factor', 0.5),
            patience=kwargs.get('patience', 10),
            min_lr=kwargs.get('min_lr', 1e-8),
            threshold=kwargs.get('threshold', 1e-4)
        )
    elif scheduler_type == "cosine":
        return CosineAnnealingLR(
            optimizer,
            T_max=kwargs.get('T_max', 50),
            eta_min=kwargs.get('eta_min', 1e-8)
        )
    elif scheduler_type == "cosine_warmup":
        return CosineAnnealingWarmupRestarts(
            optimizer,
            first_cycle_steps=kwargs.get('first_cycle_steps', 50),
            cycle_mult=kwargs.get('cycle_mult', 1.0),
            max_lr=kwargs.get('max_lr', 0.1),
            min_lr=kwargs.get('min_lr', 1e-6),
            warmup_steps=kwargs.get('warmup_steps', 5),
            gamma=kwargs.get('gamma', 1.0)
        )
    elif scheduler_type == "warmup":
        base_scheduler = None
        if 'base_scheduler_type' in kwargs:
            base_scheduler = get_scheduler(
                kwargs['base_scheduler_type'], 
                optimizer, 
                **kwargs.get('base_scheduler_kwargs', {})
            )
        return WarmupScheduler(
            optimizer,
            warmup_epochs=kwargs.get('warmup_epochs', 5),
            base_scheduler=base_scheduler
        )
    elif scheduler_type == "polynomial":
        return PolynomialLRDecay(
            optimizer,
            max_decay_steps=kwargs.get('max_decay_steps', 100),
            end_learning_rate=kwargs.get('end_learning_rate', 1e-6),
            power=kwargs.get('power', 1.0)
        )
    elif scheduler_type == "exponential":
        return ExponentialLRDecay(
            optimizer,
            decay_steps=kwargs.get('decay_steps', 10),
            decay_rate=kwargs.get('decay_rate', 0.96),
            staircase=kwargs.get('staircase', False)
        )
    elif scheduler_type == "adaptive":
        return AdaptiveLRScheduler(
            optimizer,
            patience=kwargs.get('patience', 10),
            factor=kwargs.get('factor', 0.5),
            min_lr=kwargs.get('min_lr', 1e-8)
        )
    elif scheduler_type == "onecycle":
        return OneCycleLR(
            optimizer,
            max_lr=kwargs.get('max_lr', 0.1),
            total_steps=kwargs.get('total_steps', 100),
            pct_start=kwargs.get('pct_start', 0.3),
            anneal_strategy=kwargs.get('anneal_strategy', 'cos')
        )
    else:
        raise ValueError(f"Unknown scheduler type: {scheduler_type}")
