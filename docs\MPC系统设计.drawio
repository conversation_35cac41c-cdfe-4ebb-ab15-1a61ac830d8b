<mxfile host="65bd71144e">
    <diagram id="q6hy64Q8-kN9eNDV9dDm" name="第 1 页">
        <mxGraphModel dx="914" dy="765" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="14" style="edgeStyle=none;html=1;" parent="1" source="2" target="5" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="31" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="154" y="320" as="sourcePoint"/>
                        <mxPoint x="276" y="320" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="29" value="MQTT" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="18" vertex="1" connectable="0">
                    <mxGeometry x="-0.1167" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="2" value="数据采集&lt;br&gt;Data Acquirer&lt;div&gt;（telegraf)&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="33" y="290" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="48" value="" style="edgeStyle=none;html=1;" parent="1" source="3" target="4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="MPC优化器&lt;div&gt;MPC Optimizer&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="475" y="510" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="28" style="edgeStyle=none;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="1" source="4" target="27" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="47" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 12px; text-wrap-mode: wrap; background-color: rgb(251, 251, 251);&quot;&gt;OPC UA&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="28" vertex="1" connectable="0">
                    <mxGeometry x="-0.3333" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="4" value="执行器&lt;div&gt;Actuator&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="675" y="510" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="16" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="5" target="9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="42" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="5" target="19" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="43" value="初始化时读取" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="42" vertex="1" connectable="0">
                    <mxGeometry x="0.1754" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" value="时序&lt;div&gt;数据库&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="63" y="390" width="60" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="23" style="edgeStyle=none;html=1;" parent="1" source="9" target="22" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="模型参数文件" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="23" vertex="1" connectable="0">
                    <mxGeometry x="-0.1667" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="9" value="模型训练器&lt;div&gt;Model Trainer&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="33" y="510" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="12" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="10" target="2" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="46" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 12px; text-wrap-mode: wrap; background-color: rgb(251, 251, 251);&quot;&gt;OPC UA&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="12" vertex="1" connectable="0">
                    <mxGeometry x="-0.1733" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="L1" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="33" y="180" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="31" target="19" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="335" y="460" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" value="Queue" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="35" vertex="1" connectable="0">
                    <mxGeometry x="0.0167" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="状态估计&lt;div&gt;State&amp;nbsp;Estimation&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="275" y="400" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="预测引擎&lt;br&gt;Prediction Engine" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="275" y="510" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="L1" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="675" y="180" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="31" value="MQTT监听器&lt;br&gt;&lt;div&gt;MQTT Listener&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="275" y="290" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="38" value="" style="endArrow=classic;startArrow=classic;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="22" target="3" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="395" y="590" as="sourcePoint"/>
                        <mxPoint x="465.71067811865476" y="540" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="41" value="" style="edgeStyle=none;html=1;" parent="1" source="39" target="3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="39" value="MPC定时器&lt;div&gt;MPC Timer&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="475" y="400" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="" style="endArrow=classic;startArrow=classic;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="19" target="39" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="395" y="480" as="sourcePoint"/>
                        <mxPoint x="465.71067811865476" y="430" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="45" value="Pipe" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="44" vertex="1" connectable="0">
                    <mxGeometry x="0.0333" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="49" value="StripTracker" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="33" y="880" width="120" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>