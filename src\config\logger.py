import logging
import os
from logging.handlers import TimedRotatingFileHandler

from .configs import Configs, LoggingConfig


class Logger:
    @classmethod
    def initialize(cls, log_dir: str | None = None, file_name: str | None = None):
        logger = logging.getLogger()

        logging_config = Configs.get_logging_config()
        if logging_config is None:
            logging.warning("LoggingConfig 未初始化")
            logging_config = LoggingConfig()

        if log_dir is None:
            log_dir = logging_config.log_dir
        if file_name is None:
            file_name = logging_config.file_name

        file_path = os.path.join(log_dir, file_name)

        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        if not os.path.exists(os.path.dirname(file_path)):
            os.makedirs(os.path.dirname(file_path))

        logger.setLevel(logging_config.level)

        if logging_config.file_handler:
            file_handler = TimedRotatingFileHandler(
                file_path,
                when="midnight",
                interval=1,
                backupCount=logging_config.max_days,
                encoding="utf-8",
            )
            file_handler.setLevel(logging_config.level)
            file_handler.setFormatter(
                logging.Formatter(
                    logging_config.format,
                    datefmt=logging_config.date_format,
                )
            )
            logger.addHandler(file_handler)

        if logging_config.console_handler:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging_config.level)
            console_handler.setFormatter(
                logging.Formatter(
                    logging_config.format,
                    datefmt=logging_config.date_format,
                )
            )
            logger.addHandler(console_handler)
