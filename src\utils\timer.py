import functools
import logging
import time

_logger = logging.getLogger(__name__)


def timer(func):
    """
    一个通用的函数计时装饰器。
    打印函数执行所需的总时间。
    """

    # @functools.wraps(func) 是一个重要的辅助装饰器，
    # 它可以保留原函数的元信息（如函数名 __name__、文档字符串 __doc__ 等）
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        """包装函数，执行计时逻辑"""
        start_time = time.perf_counter()  # 使用 perf_counter() 提供高精度计时

        # 调用原始函数，并获取其返回值
        result = func(*args, **kwargs)

        end_time = time.perf_counter()
        elapsed_time_ms = (end_time - start_time) * 1000

        # 打印格式化的计时信息
        _logger.info(f"函数 '{func.__name__}' 执行完毕，耗时: {elapsed_time_ms:.2f} 毫秒")

        # 返回原始函数的执行结果
        return result

    return wrapper
