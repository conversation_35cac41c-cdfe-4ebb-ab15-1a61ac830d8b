<mxfile host="65bd71144e">
    <diagram id="4aEQTPoJTsNqi8XtSpSI" name="第 1 页">
        <mxGraphModel dx="1406" dy="743" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="9" style="edgeStyle=none;html=1;" edge="1" parent="1" source="5" target="4">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="Strip" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=50;horizontalStack=0;rounded=1;fontSize=14;fontStyle=0;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;arcSize=4;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry y="280" width="100" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="- name&lt;br&gt;- type&lt;br&gt;- width&lt;div&gt;- thick&lt;/div&gt;&lt;div&gt;- length&lt;/div&gt;&lt;div&gt;- weight&lt;/div&gt;" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;html=1;" vertex="1" parent="4">
                    <mxGeometry y="50" width="100" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="TrackMsg" style="swimlane;childLayout=stackLayout;horizontal=1;startSize=50;horizontalStack=0;rounded=1;fontSize=14;fontStyle=0;strokeWidth=2;resizeParent=0;resizeLast=1;shadow=0;dashed=0;align=center;arcSize=4;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="140" y="280" width="100" height="160" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="- timestamp&lt;br&gt;&lt;div&gt;- selected&lt;/div&gt;&lt;div&gt;- strip1&lt;/div&gt;&lt;div&gt;- strip2&lt;/div&gt;&lt;div&gt;- weld1&lt;/div&gt;&lt;div&gt;- weld2&lt;/div&gt;&lt;div&gt;- speed&lt;/div&gt;" style="align=left;strokeColor=none;fillColor=none;spacingLeft=4;fontSize=12;verticalAlign=top;resizable=0;rotatable=0;part=1;html=1;" vertex="1" parent="6">
                    <mxGeometry y="50" width="100" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="360" y="120" width="120" height="60" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>